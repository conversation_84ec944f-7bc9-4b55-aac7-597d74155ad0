# Question Bank Move Web

题库移动端应用，基于 Vue 3 + Vite + TypeScript + Vant 构建的移动端 Web 应用。

## 功能特性

- 📱 移动端优化的响应式设计
- 📄 支持多种文件格式的在线预览（Word、Excel、PowerPoint、PDF、视频等）
- 🔍 文件预览支持水印、新窗口打开等功能
- 🎨 基于 Vant 组件库的现代化 UI
- 🔧 完整的开发工具链配置

## 技术栈

- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI 组件库**: Vant 4
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: Less + UnoCSS
- **代码规范**: ESLint + Prettier

## 快速开始

### 环境要求

- Node.js >= 16
- pnpm >= 7

### 安装依赖

```bash
pnpm install
```

### 环境配置

1. 复制环境变量示例文件：
```bash
cp .env.example .env.local
```

2. 根据实际环境修改 `.env.local` 中的配置项

### 开发

```bash
pnpm dev
```

### 构建

```bash
pnpm build
```

## 环境变量配置

项目使用环境变量进行配置，所有环境变量都以 `MK_APP_` 为前缀。

### 主要配置项

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `MK_APP_TITLE` | 应用标题 | `题库移动端` |
| `MK_APP_API_URL` | 主要 API 接口地址 | `http://api.example.com/` |
| `MK_APP_ALG_API_URL` | 算法 API 接口地址 | `http://algorithm-api.example.com/` |
| `MK_APP_PREVIEW_BASE` | 文件预览服务地址 | `http://preview.example.com` |
| `MK_APP_GUC_APPID` | GUC 应用 ID | `your-guc-app-id` |
| `MK_APP_GUC_API_URL` | GUC API 地址 | `https://guc-api.example.com/` |
| `MK_APP_ONLINE_MONITOR_URL` | 监控服务地址 | `https://monitor.example.com/` |
| `MK_APP_ONLINE_MONITOR_SERVICE` | 监控服务名称 | `question-bank-move-web` |
| `MK_APP_ONLINE_MONITOR_APPID` | 监控应用 ID | `your-monitor-app-id` |
| `MK_APP_PRIVATE_DEPLOY` | 是否私有化部署 | `false` |

### 文件预览配置

`MK_APP_PREVIEW_BASE` 环境变量用于配置文件预览服务的基础地址。该服务支持以下文件格式的在线预览：

- **文档**: Word (.doc, .docx)
- **表格**: Excel (.xls, .xlsx)
- **演示**: PowerPoint (.ppt, .pptx)
- **PDF**: PDF 文档
- **视频**: MP4、AVI 等视频格式

如果未配置此环境变量，将使用默认的预览服务地址。

## 开发配置

### 开发代理配置

项目使用 `dev.config.ts` 文件配置开发环境的代理设置：

```bash
cp dev.config.ts.example dev.config.ts
```

然后根据需要修改代理配置。

### 代码规范

项目配置了完整的代码规范工具：

```bash
# 代码检查
pnpm lint

# 代码格式化（通过 lint-staged 在提交时自动执行）
```

## 项目结构

```
src/
├── api/                 # API 接口定义
├── assets/             # 静态资源
├── components/         # 公共组件
│   ├── MediaViewer/    # 媒体查看器组件
│   └── ...
├── config/             # 配置文件
├── env.ts              # 环境变量定义
├── hooks/              # 自定义 hooks
├── pages/              # 页面组件
├── router/             # 路由配置
├── store/              # 状态管理
├── types/              # 类型定义
└── utils/              # 工具函数
```

## 部署

### Docker 部署

项目包含 Dockerfile 和 docker-compose 配置：

```bash
# 构建镜像
docker build -t question-bank-move-web .

# 使用 docker-compose 启动
docker-compose up -d
```

### 环境变量注入

在容器化部署时，可以通过环境变量动态配置：

- `API`: 后端 API 地址
- `GUC`: GUC 服务地址
- `MINIO`: 文件存储服务地址

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
