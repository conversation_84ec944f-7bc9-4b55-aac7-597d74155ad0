# 文件预览组件使用说明

## 概述

`FilePreview` 组件支持多种文件格式的在线预览，包括 Word、Excel、PowerPoint、PDF 和视频文件。组件会自动根据环境变量 `MK_APP_PREVIEW_BASE` 配置预览服务地址。

## 环境变量配置

在 `.env.local` 或相应的环境配置文件中设置：

```bash
# 文件预览服务基础地址
MK_APP_PREVIEW_BASE=http://your-preview-service.com
```

如果未设置此环境变量，组件将使用默认的预览服务地址。

## 基本使用

### 1. 直接使用 FilePreview 组件

```vue
<template>
  <FilePreview
    :file-url="fileUrl"
    :file-name="fileName"
    :file-type="fileType"
    :file-size="fileSize"
    :create-time="createTime"
    :water-mark="waterMark"
  />
</template>

<script setup>
import FilePreview from '@/components/MediaViewer/FilePreview.vue'

const fileUrl = 'https://example.com/document.docx'
const fileName = '示例文档.docx'
const fileType = 'docx'
const fileSize = 1024000 // 文件大小（字节）
const createTime = '2024-01-01T00:00:00Z'
const waterMark = '智工育匠'
</script>
```

### 2. 使用 MediaViewer 组件（推荐）

```vue
<template>
  <MediaViewer
    :media-item="mediaItem"
    :show-title="true"
    :water-mark="waterMark"
  />
</template>

<script setup>
import MediaViewer from '@/components/MediaViewer/MediaViewer.vue'

const mediaItem = {
  id: '1',
  name: '示例文档.docx',
  url: 'https://example.com/document.docx',
  type: 'docx',
  size: 1024000,
  createTime: '2024-01-01T00:00:00Z'
}

const waterMark = '智工育匠'
</script>
```

## 组件属性

### FilePreview 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `fileUrl` | `string` | - | 文件 URL（必需） |
| `fileName` | `string` | `''` | 文件名称 |
| `fileType` | `string` | `''` | 文件类型 |
| `fileSize` | `number` | - | 文件大小（字节） |
| `previewBaseUrl` | `string` | 环境变量值 | 预览服务基础地址 |
| `moduleType` | `number` | `9` | 模块类型 |
| `waterMark` | `string` | `'智工育匠'` | 水印文字 |
| `createTime` | `string` | `''` | 创建时间 |

### MediaViewer 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `mediaItem` | `MediaItem` | - | 媒体项目对象（必需） |
| `showTitle` | `boolean` | `true` | 是否显示标题 |
| `autoplay` | `boolean` | `false` | 视频是否自动播放 |
| `allowDownload` | `boolean` | `false` | 是否允许下载 |
| `waterMark` | `string` | `'智工育匠'` | 水印文字 |

## 支持的文件格式

- **文档**: .doc, .docx
- **表格**: .xls, .xlsx
- **演示**: .ppt, .pptx
- **PDF**: .pdf
- **视频**: .mp4, .avi

## 自定义预览服务地址

如果需要为特定文件使用不同的预览服务，可以通过 `previewBaseUrl` 属性覆盖默认配置：

```vue
<template>
  <FilePreview
    :file-url="fileUrl"
    :file-name="fileName"
    preview-base-url="http://custom-preview-service.com"
  />
</template>
```

## 注意事项

1. 文件大小限制为 1GB，超过限制的文件将显示错误提示
2. 预览服务需要支持跨域访问
3. 确保预览服务地址可以正常访问
4. 建议在生产环境中使用 HTTPS 协议的预览服务地址
