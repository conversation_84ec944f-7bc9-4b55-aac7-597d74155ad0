# 应用配置示例文件
# 复制此文件为 .env.local 或 .env.development 等，并根据实际环境配置相应的值

# 应用标题
MK_APP_TITLE=题库移动端

# API 接口地址
MK_APP_API_URL=http://intelli-matching-service.caas-cloud-dev.geega.com/

# 算法 API 接口地址  
MK_APP_ALG_API_URL=http://algorithm-api.example.com/

# GUC 配置
MK_APP_GUC_APPID=your-guc-app-id
MK_APP_GUC_API_URL=https://guc3-api-test.geega.com/

# 在线监控配置
MK_APP_ONLINE_MONITOR_URL=https://onlinemonitor-server-dev.geega.com/
MK_APP_ONLINE_MONITOR_SERVICE=question-bank-move-web
MK_APP_ONLINE_MONITOR_APPID=your-monitor-app-id

# 是否是私有化部署
MK_APP_PRIVATE_DEPLOY=false

# 文件预览服务基础地址
# 用于配置文档、表格、演示文稿等文件的在线预览服务地址
MK_APP_PREVIEW_BASE=http://kkvf-big-excel-view.caas-cloud-test.geega.com
